<?php
/**
 * Script untuk memeriksa path gambar di database
 */

require_once 'config.php';

echo "<h2>🔍 Check Database Images</h2>";

try {
    $pdo = getConnection();

    // Get all news with images
    echo "<h3>1. News with Images (Latest 10)</h3>";
    $stmt = $pdo->prepare("SELECT id, title, image, image_base64, created_at FROM posts WHERE image IS NOT NULL AND image != '' ORDER BY created_at DESC LIMIT 10");
    $stmt->execute();
    $newsWithImages = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "Found " . count($newsWithImages) . " news with images<br><br>";

    if (count($newsWithImages) == 0) {
        echo "❌ No news with images found<br>";
        echo "Please add some news with images first.<br>";
        exit;
    }

    // Display news with images
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Title</th><th>Image Path</th><th>Has Base64</th><th>Created</th></tr>";

    foreach ($newsWithImages as $news) {
        echo "<tr>";
        echo "<td>" . $news['id'] . "</td>";
        echo "<td>" . htmlspecialchars(substr($news['title'], 0, 50)) . "</td>";
        echo "<td>" . htmlspecialchars($news['image']) . "</td>";
        echo "<td>" . (!empty($news['image_base64']) ? 'Yes' : 'No') . "</td>";
        echo "<td>" . $news['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";

    // Check file existence
    echo "<h3>2. File Existence Check</h3>";
    $frontendUploadDir = __DIR__ . '/../../../uploads/';
    $rootUploadDir = $_SERVER['DOCUMENT_ROOT'] . '/uploads/';

    echo "Frontend uploads directory: " . $frontendUploadDir . "<br>";
    echo "Root uploads directory: " . $rootUploadDir . "<br><br>";

    foreach ($newsWithImages as $news) {
        $imagePath = $news['image'];
        if ($imagePath) {
            echo "<strong>News ID {$news['id']}: {$news['title']}</strong><br>";
            echo "Database path: " . htmlspecialchars($imagePath) . "<br>";

            // Extract filename
            $filename = '';
            if (strpos($imagePath, '/uploads/') === 0) {
                $filename = str_replace('/uploads/', '', $imagePath);
            } else if (strpos($imagePath, '/react-news/uploads/') === 0) {
                $filename = str_replace('/react-news/uploads/', '', $imagePath);
            } else if (strpos($imagePath, '/react-news/frontend/uploads/') === 0) {
                $filename = str_replace('/react-news/frontend/uploads/', '', $imagePath);
            } else if (!strpos($imagePath, '/')) {
                $filename = $imagePath;
            } else {
                $filename = basename($imagePath);
            }

            echo "Extracted filename: " . $filename . "<br>";

            // Check in frontend uploads
            $frontendFile = $frontendUploadDir . $filename;
            $frontendExists = file_exists($frontendFile);
            echo "Frontend file exists: " . ($frontendExists ? '✅ Yes' : '❌ No') . " - " . $frontendFile . "<br>";

            // Check in root uploads
            $rootFile = $rootUploadDir . $filename;
            $rootExists = file_exists($rootFile);
            echo "Root file exists: " . ($rootExists ? '✅ Yes' : '❌ No') . " - " . $rootFile . "<br>";

            echo "<br>";
        }
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Title</th><th>Image Path</th><th>Has Base64</th><th>File Exists</th><th>URL Test</th><th>Created</th></tr>";
    
    foreach ($newsWithImages as $news) {
        $imagePath = $news['image'];
        $hasBase64 = !empty($news['image_base64']) ? 'Yes' : 'No';
        
        // Check if file exists
        $fileExists = 'No';
        $testUrl = '';
        
        if ($imagePath) {
            // Extract filename from path
            $filename = '';
            if (strpos($imagePath, '/uploads/') === 0) {
                $filename = str_replace('/uploads/', '', $imagePath);
            } else if (strpos($imagePath, '/react-news/uploads/') === 0) {
                $filename = str_replace('/react-news/uploads/', '', $imagePath);
            } else if (!strpos($imagePath, '/')) {
                $filename = $imagePath;
            } else {
                $filename = basename($imagePath);
            }
            
            // Check if file exists in uploads folder
            $filePath = __DIR__ . '/../../../uploads/' . $filename;
            if (file_exists($filePath)) {
                $fileExists = 'Yes';
                $testUrl = "http://localhost/react-news/uploads/" . $filename;
            } else {
                $fileExists = 'No - ' . $filePath;
            }
        }
        
        echo "<tr>";
        echo "<td>" . $news['id'] . "</td>";
        echo "<td>" . htmlspecialchars(substr($news['title'], 0, 30)) . "...</td>";
        echo "<td>" . htmlspecialchars($imagePath) . "</td>";
        echo "<td>" . $hasBase64 . "</td>";
        echo "<td>" . $fileExists . "</td>";
        echo "<td>" . ($testUrl ? "<a href='$testUrl' target='_blank'>Test</a>" : 'N/A') . "</td>";
        echo "<td>" . $news['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check uploads directory
    echo "<h3>2. Files in Uploads Directory</h3>";
    $uploadDir = __DIR__ . '/../../../uploads/';
    
    if (is_dir($uploadDir)) {
        $files = glob($uploadDir . 'news_*');
        echo "<strong>News Images Found:</strong> " . count($files) . "<br>";
        
        if (count($files) > 0) {
            echo "<strong>Recent Files:</strong><br>";
            $recentFiles = array_slice($files, -10);
            foreach ($recentFiles as $file) {
                $filename = basename($file);
                $webUrl = "http://localhost/react-news/uploads/" . $filename;
                echo "   • " . $filename . " - <a href='" . $webUrl . "' target='_blank'>View</a><br>";
            }
        }
    } else {
        echo "❌ Uploads directory not found: " . $uploadDir . "<br>";
    }
    
    // Check for missing files
    echo "<h3>3. Missing Files Analysis</h3>";
    $missingFiles = [];
    
    foreach ($newsWithImages as $news) {
        $imagePath = $news['image'];
        if ($imagePath) {
            $filename = '';
            if (strpos($imagePath, '/uploads/') === 0) {
                $filename = str_replace('/uploads/', '', $imagePath);
            } else if (strpos($imagePath, '/react-news/uploads/') === 0) {
                $filename = str_replace('/react-news/uploads/', '', $imagePath);
            } else if (!strpos($imagePath, '/')) {
                $filename = $imagePath;
            } else {
                $filename = basename($imagePath);
            }
            
            $filePath = __DIR__ . '/../../../uploads/' . $filename;
            if (!file_exists($filePath)) {
                $missingFiles[] = [
                    'id' => $news['id'],
                    'title' => $news['title'],
                    'path' => $imagePath,
                    'filename' => $filename,
                    'expected_path' => $filePath
                ];
            }
        }
    }
    
    if (count($missingFiles) > 0) {
        echo "<strong>Missing Files:</strong> " . count($missingFiles) . "<br>";
        foreach ($missingFiles as $missing) {
            echo "   • ID " . $missing['id'] . ": " . htmlspecialchars($missing['title']) . "<br>";
            echo "     Database path: " . htmlspecialchars($missing['path']) . "<br>";
            echo "     Expected file: " . $missing['expected_path'] . "<br><br>";
        }
    } else {
        echo "✅ All files found!<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}
?>
