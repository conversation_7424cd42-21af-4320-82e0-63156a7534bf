<?php
// Simple database check
try {
    $pdo = new PDO('mysql:host=localhost;dbname=react_news', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful\n";
    
    // Check if posts table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'posts'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Posts table exists\n";
        
        // Get posts with images
        $stmt = $pdo->query("SELECT id, title, image FROM posts WHERE image IS NOT NULL AND image != '' LIMIT 5");
        $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Found " . count($posts) . " posts with images:\n";
        foreach ($posts as $post) {
            echo "- ID: {$post['id']}, Title: " . substr($post['title'], 0, 30) . "..., Image: {$post['image']}\n";
        }
    } else {
        echo "❌ Posts table does not exist\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
?>
