<?php
/**
 * Comprehensive Image Synchronization Script
 * Migrates images from frontend/uploads to root/uploads and updates database paths
 */

// Database configuration
$host = 'localhost';
$dbname = 'react_news';
$username = 'root';
$password = '';

// Directory paths
$frontendUploadDir = __DIR__ . '/frontend/uploads/';
$rootUploadDir = __DIR__ . '/uploads/';

echo "🔄 Starting Image Synchronization...\n";
echo "From: $frontendUploadDir\n";
echo "To: $rootUploadDir\n\n";

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful\n\n";
    
    // Create root uploads directory if it doesn't exist
    if (!is_dir($rootUploadDir)) {
        if (mkdir($rootUploadDir, 0755, true)) {
            echo "✅ Created root uploads directory\n";
        } else {
            echo "❌ Failed to create root uploads directory\n";
            exit(1);
        }
    } else {
        echo "✅ Root uploads directory exists\n";
    }
    
    // Check frontend uploads directory
    if (!is_dir($frontendUploadDir)) {
        echo "❌ Frontend uploads directory not found\n";
        exit(1);
    }
    
    echo "✅ Frontend uploads directory exists\n\n";
    
    // Get all image files from frontend uploads
    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'svg'];
    $imageFiles = [];
    
    $files = scandir($frontendUploadDir);
    foreach ($files as $file) {
        if ($file === '.' || $file === '..') continue;
        
        $filePath = $frontendUploadDir . $file;
        if (is_file($filePath)) {
            $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
            if (in_array($extension, $imageExtensions)) {
                $imageFiles[] = $file;
            }
        }
    }
    
    echo "📁 Found " . count($imageFiles) . " image files in frontend/uploads\n";
    
    if (count($imageFiles) === 0) {
        echo "ℹ️  No image files to migrate\n";
    } else {
        echo "📋 Image files to migrate:\n";
        foreach ($imageFiles as $file) {
            echo "   - $file\n";
        }
        echo "\n";
    }
    
    // Migrate files
    $migratedCount = 0;
    $errorCount = 0;
    $skippedCount = 0;
    
    foreach ($imageFiles as $file) {
        $sourcePath = $frontendUploadDir . $file;
        $destPath = $rootUploadDir . $file;
        
        // Check if destination file already exists
        if (file_exists($destPath)) {
            // Compare file sizes to see if they're the same
            if (filesize($sourcePath) === filesize($destPath)) {
                echo "⏭️  Skipped $file (already exists with same size)\n";
                $skippedCount++;
                continue;
            } else {
                // Create a unique name for the new file
                $pathInfo = pathinfo($file);
                $newName = $pathInfo['filename'] . '_migrated_' . time() . '.' . $pathInfo['extension'];
                $destPath = $rootUploadDir . $newName;
                echo "🔄 Renaming $file to $newName (destination exists with different size)\n";
            }
        }
        
        // Copy file
        if (copy($sourcePath, $destPath)) {
            echo "✅ Migrated: $file\n";
            $migratedCount++;
        } else {
            echo "❌ Failed to migrate: $file\n";
            $errorCount++;
        }
    }
    
    echo "\n📊 Migration Summary:\n";
    echo "   Migrated: $migratedCount files\n";
    echo "   Skipped: $skippedCount files\n";
    echo "   Errors: $errorCount files\n\n";
    
    // Update database paths
    echo "🔄 Updating database paths...\n";
    
    // Get all posts with images
    $stmt = $pdo->query("SELECT id, title, image FROM posts WHERE image IS NOT NULL AND image != ''");
    $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($posts) . " posts with images in database\n";
    
    $updatedCount = 0;
    foreach ($posts as $post) {
        $currentPath = $post['image'];
        $newPath = null;
        
        // Determine new path based on current path
        if (strpos($currentPath, '/react-news/frontend/uploads/') === 0) {
            $filename = str_replace('/react-news/frontend/uploads/', '', $currentPath);
            $newPath = '/uploads/' . $filename;
        } elseif (strpos($currentPath, '/frontend/uploads/') === 0) {
            $filename = str_replace('/frontend/uploads/', '', $currentPath);
            $newPath = '/uploads/' . $filename;
        } elseif (strpos($currentPath, 'assets/news/') === 0) {
            $filename = str_replace('assets/news/', '', $currentPath);
            $newPath = '/uploads/' . $filename;
        } elseif (!strpos($currentPath, '/') && in_array(strtolower(pathinfo($currentPath, PATHINFO_EXTENSION)), $imageExtensions)) {
            // Just filename
            $newPath = '/uploads/' . $currentPath;
        }
        
        if ($newPath && $newPath !== $currentPath) {
            // Check if the file actually exists in the new location
            $actualFile = $rootUploadDir . basename($newPath);
            if (file_exists($actualFile)) {
                $updateStmt = $pdo->prepare("UPDATE posts SET image = ? WHERE id = ?");
                if ($updateStmt->execute([$newPath, $post['id']])) {
                    echo "✅ Updated post {$post['id']}: {$currentPath} → {$newPath}\n";
                    $updatedCount++;
                } else {
                    echo "❌ Failed to update post {$post['id']}\n";
                }
            } else {
                echo "⚠️  File not found for post {$post['id']}: $actualFile\n";
            }
        }
    }
    
    echo "\n📊 Database Update Summary:\n";
    echo "   Updated: $updatedCount posts\n\n";
    
    // Verify results
    echo "🔍 Verification:\n";
    $stmt = $pdo->query("SELECT id, title, image FROM posts WHERE image LIKE '/uploads/%' LIMIT 5");
    $updatedPosts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Sample updated posts:\n";
    foreach ($updatedPosts as $post) {
        $filePath = $rootUploadDir . basename($post['image']);
        $exists = file_exists($filePath) ? '✅' : '❌';
        echo "   {$exists} ID {$post['id']}: {$post['image']}\n";
    }
    
    echo "\n✅ Image synchronization completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
