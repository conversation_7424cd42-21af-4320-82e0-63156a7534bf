[{"C:\\laragon\\www\\react-news\\frontend\\src\\index.js": "1", "C:\\laragon\\www\\react-news\\frontend\\src\\App.js": "2", "C:\\laragon\\www\\react-news\\frontend\\src\\reportWebVitals.js": "3", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\LandingPage.js": "4", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\WebsitePage.js": "5", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Navbar.js": "6", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Footer.js": "7", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\CategorySlider.js": "8", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\NewsCard.js": "9", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\PopularNews.js": "10", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Register.js": "11", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\swal.js": "12", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\MainNewsCards.js": "13", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Saved.js": "14", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\views.js": "15", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\DashboardContent.js": "16", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\BeritaContent.js": "17", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\PengaturanContent.js": "18", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\authService.js": "19", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\data-news.js": "20", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\ForgotPassword.js": "21", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\Register.js": "22", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\Login.js": "23", "C:\\laragon\\www\\react-news\\frontend\\src\\contexts\\AuthContext.js": "24", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\AdminLogin.js": "25", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\VideoPage.js": "26", "C:\\laragon\\www\\react-news\\frontend\\src\\components\\VideoFeed.js": "27"}, {"size": 535, "mtime": 1752195971363, "results": "28", "hashOfConfig": "29"}, {"size": 3292, "mtime": 1752575597713, "results": "30", "hashOfConfig": "29"}, {"size": 362, "mtime": 1752195971670, "results": "31", "hashOfConfig": "29"}, {"size": 94937, "mtime": 1752656339918, "results": "32", "hashOfConfig": "29"}, {"size": 243, "mtime": 1752201497899, "results": "33", "hashOfConfig": "29"}, {"size": 662, "mtime": 1752201773682, "results": "34", "hashOfConfig": "29"}, {"size": 313, "mtime": 1752201773682, "results": "35", "hashOfConfig": "29"}, {"size": 822, "mtime": 1752201773678, "results": "36", "hashOfConfig": "29"}, {"size": 688, "mtime": 1752201773679, "results": "37", "hashOfConfig": "29"}, {"size": 749, "mtime": 1752201773681, "results": "38", "hashOfConfig": "29"}, {"size": 5407, "mtime": 1752367525610, "results": "39", "hashOfConfig": "29"}, {"size": 218, "mtime": 1752206025063, "results": "40", "hashOfConfig": "29"}, {"size": 4157, "mtime": 1752220101050, "results": "41", "hashOfConfig": "29"}, {"size": 16282, "mtime": 1752650216074, "results": "42", "hashOfConfig": "29"}, {"size": 0, "mtime": 1752306675896, "results": "43", "hashOfConfig": "29"}, {"size": 11682, "mtime": 1752310168602, "results": "44", "hashOfConfig": "29"}, {"size": 13390, "mtime": 1752310218662, "results": "45", "hashOfConfig": "29"}, {"size": 16930, "mtime": 1752310279691, "results": "46", "hashOfConfig": "29"}, {"size": 4742, "mtime": 1752366365655, "results": "47", "hashOfConfig": "29"}, {"size": 22403, "mtime": 1752656565893, "results": "48", "hashOfConfig": "29"}, {"size": 13709, "mtime": 1752547126488, "results": "49", "hashOfConfig": "29"}, {"size": 14618, "mtime": 1752547105182, "results": "50", "hashOfConfig": "29"}, {"size": 8407, "mtime": 1752547081826, "results": "51", "hashOfConfig": "29"}, {"size": 4948, "mtime": 1752473947681, "results": "52", "hashOfConfig": "29"}, {"size": 10520, "mtime": 1752575575599, "results": "53", "hashOfConfig": "29"}, {"size": 11436, "mtime": 1752575634235, "results": "54", "hashOfConfig": "29"}, {"size": 31600, "mtime": 1752648552498, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1umod7j", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\laragon\\www\\react-news\\frontend\\src\\index.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\App.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\reportWebVitals.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\LandingPage.js", ["137"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\WebsitePage.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Navbar.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Footer.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\CategorySlider.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\NewsCard.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\PopularNews.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Register.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\swal.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\MainNewsCards.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Saved.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\views.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\DashboardContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\BeritaContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\PengaturanContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\authService.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\data-news.js", ["138", "139", "140", "141"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\ForgotPassword.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\Register.js", ["142"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\auth\\Login.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\AdminLogin.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\VideoPage.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\components\\VideoFeed.js", ["143", "144", "145"], [], {"ruleId": "146", "severity": 1, "message": "147", "line": 18, "column": 8, "nodeType": "148", "messageId": "149", "endLine": 18, "endColumn": 14}, {"ruleId": "146", "severity": 1, "message": "150", "line": 18, "column": 11, "nodeType": "148", "messageId": "149", "endLine": 18, "endColumn": 20}, {"ruleId": "146", "severity": 1, "message": "151", "line": 24, "column": 12, "nodeType": "148", "messageId": "149", "endLine": 24, "endColumn": 21}, {"ruleId": "146", "severity": 1, "message": "152", "line": 24, "column": 23, "nodeType": "148", "messageId": "149", "endLine": 24, "endColumn": 35}, {"ruleId": "153", "severity": 1, "message": "154", "line": 32, "column": 8, "nodeType": "155", "endLine": 32, "endColumn": 12, "suggestions": "156"}, {"ruleId": "146", "severity": 1, "message": "157", "line": 33, "column": 11, "nodeType": "148", "messageId": "149", "endLine": 33, "endColumn": 19}, {"ruleId": "146", "severity": 1, "message": "158", "line": 18, "column": 8, "nodeType": "148", "messageId": "149", "endLine": 18, "endColumn": 17}, {"ruleId": "146", "severity": 1, "message": "159", "line": 79, "column": 9, "nodeType": "148", "messageId": "149", "endLine": 79, "endColumn": 20}, {"ruleId": "146", "severity": 1, "message": "160", "line": 80, "column": 9, "nodeType": "148", "messageId": "149", "endLine": 80, "endColumn": 19}, "no-unused-vars", "'Footer' is defined but never used.", "Identifier", "unusedVar", "'isDesktop' is assigned a value but never used.", "'bottomNav' is assigned a value but never used.", "'setBottomNav' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchNewsDetail'. Either include it or remove the dependency array.", "ArrayExpression", ["161"], "'register' is assigned a value but never used.", "'PauseIcon' is defined but never used.", "'videoHeight' is assigned a value but never used.", "'videoWidth' is assigned a value but never used.", {"desc": "162", "fix": "163"}, "Update the dependencies array to be: [fetchNewsDetail, id]", {"range": "164", "text": "165"}, [1246, 1250], "[fetchNewsDetail, id]"]